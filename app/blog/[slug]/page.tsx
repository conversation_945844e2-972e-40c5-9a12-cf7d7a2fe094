// app/blog/[slug]/page.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { ArrowLeft, Calendar, Clock, Share2, Tag, Edit, Trash } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import BaseLayout from '@/components/BaseLayout';
import Logo from "@/components/Logo";
import { MatrixText } from '@/components/matrix/MatrixText';
import { TerminalWindow } from '@/components/matrix/TerminalWindow';
import { useAuth } from '@/context/AuthContext';
import { useBlogs } from '@/hooks/useBlogs';
import BlogModal from '@/components/BlogModal';
import { BlogPost } from '@/types/blog';
import dynamic from 'next/dynamic';
import '@uiw/react-md-editor/markdown-editor.css';
import '@uiw/react-markdown-preview/markdown.css';


const MDPreview = dynamic(
    () => import('@uiw/react-markdown-preview'),
    { ssr: false }
);
const BlogPostDetail = () => {
    const { isAdmin } = useAuth();
    const params = useParams();
    const router = useRouter();
    const { getBlogBySlug, deleteBlog } = useBlogs();
    const [blog, setBlog] = useState<BlogPost | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [loading, setLoading] = useState(true);

    const loadBlog = async () => {
        if (!params.slug) return;

        try {
            setLoading(true);
            const blogData = await getBlogBySlug(params.slug as string);
            if (!blogData) {
                router.push('/blog');
                return;
            }
            if (!blogData.published && !isAdmin) {
                router.push('/blog');
                return;
            }
            console.log('Blog data loaded:', blogData);
            console.log('Blog content:', blogData.content);
            setBlog(blogData);
        } catch (error) {
            console.error('Error loading blog:', error);
            router.push('/blog');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (params.slug) {
            loadBlog();
        }
    }, [params.slug]); // Sadece slug değiştiğinde yeniden yükle

    const handleDelete = async () => {
        if (!blog) return;
        if (window.confirm('Are you sure you want to delete this post?')) {
            await deleteBlog(blog.id);
            router.push('/blog');
        }
    };

    if (loading) {
        return (
            <BaseLayout>
                <div className="animate-pulse">
                    {/* Loading skeleton */}
                </div>
            </BaseLayout>
        );
    }

    if (!blog) {
        console.log('Blog is null, showing not found');
        return <BaseLayout><div className="text-white p-8">Post not found</div></BaseLayout>;
    }

    console.log('Rendering blog:', blog.title);

    // Schema.org yapılandırması
    const blogSchema = {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "headline": blog.title,
        "description": blog.excerpt,
        "image": blog.image,
        "datePublished": blog.createdAt,
        "dateModified": blog.updatedAt,
        "author": {
            "@type": "Person",
            "name": "Miktad Tahir"
        },
        "publisher": {
            "@type": "Person",
            "name": "Miktad Tahir"
        },
        "keywords": blog.tags.join(", ")
    };

    return (
        <BaseLayout>
            <Head>
                <title>{`${blog.title} | Neural Network Logs`}</title>
                <meta name="description" content={blog.excerpt} />
                <meta name="keywords" content={blog.tags.join(", ")} />
                <meta property="og:title" content={blog.title} />
                <meta property="og:description" content={blog.excerpt} />
                <meta property="og:image" content={blog.image} />
                <meta property="og:type" content="article" />
                <meta name="twitter:card" content="summary_large_image" />
                <script type="application/ld+json">
                    {JSON.stringify(blogSchema)}
                </script>
            </Head>

            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
            >
                <Link
                    href="/blog"
                    className="inline-flex items-center text-white/80 hover:text-white
                        transition-colors group bg-white/5 backdrop-blur-sm
                        px-4 py-2 rounded-full hover:bg-white/10"
                >
                    <ArrowLeft className="mr-2 w-4 h-4 group-hover:-translate-x-1 transition-transform" />
                    <Logo className="w-6 h-6 text-white" />
                    <span className="ml-2">
                        <MatrixText text="Return to Neural Network" />
                    </span>
                </Link>
            </motion.div>

            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="relative group"
            >
                <TerminalWindow
                    title={`Neural_Log_${blog.slug}.dmg`}
                    className="backdrop-blur-lg bg-white/10 rounded-3xl shadow-xl"
                >
                    <div>
                        {/* Admin Controls */}
                        {isAdmin && (
                            <div className="absolute top-2 right-2 z-10 flex gap-2">
                                <button
                                    onClick={() => setIsModalOpen(true)}
                                    className="p-2 rounded-full bg-white/10 hover:bg-white/20
                                        transition-colors opacity-0 group-hover:opacity-100"
                                >
                                    <Edit className="w-4 h-4 text-white/80" />
                                </button>
                                <button
                                    onClick={handleDelete}
                                    className="p-2 rounded-full bg-white/10 hover:bg-white/20
                                        transition-colors opacity-0 group-hover:opacity-100"
                                >
                                    <Trash className="w-4 h-4 text-white/80" />
                                </button>
                            </div>
                        )}

                        {/* Cover Image */}
                        <div className="relative w-full h-[300px] rounded-t-xl overflow-hidden">
                            <Image
                                src={blog.image}
                                alt={blog.title}
                                fill
                                className="object-cover"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                        </div>

                        {/* Content */}
                        <div className="p-6">
                            <h1 className="text-3xl font-bold text-white mb-4">
                                {blog.title}
                            </h1>

                            <div className="flex items-center gap-6 mb-6">
                                <div className="flex items-center gap-2 text-white/60">
                                    <Calendar className="w-4 h-4" />
                                    <span className="text-sm">{blog.date}</span>
                                </div>
                                <div className="flex items-center gap-2 text-white/60">
                                    <Clock className="w-4 h-4" />
                                    <span className="text-sm">{blog.readTime}</span>
                                </div>
                                <button
                                    onClick={() => navigator.clipboard.writeText(window.location.href)}
                                    className="flex items-center gap-2 text-white/60 hover:text-white transition-colors ml-auto"
                                >
                                    <Share2 className="w-4 h-4" />
                                    <span className="text-sm">Share</span>
                                </button>
                            </div>

                            {/* Tags */}
                            <div className="flex flex-wrap gap-2 mb-6">
                                {blog.tags.map(tag => (
                                    <span
                                        key={tag}
                                        className="inline-flex items-center gap-1 text-sm text-white/60
                                            bg-white/5 px-3 py-1 rounded-full"
                                    >
                                        <Tag className="w-3 h-3" />
                                        {tag}
                                    </span>
                                ))}
                            </div>

                            {/* Blog Content */}
                            <div className="prose prose-invert prose-green max-w-none">
                                <div data-color-mode="dark">
                                    {blog.content && (
                                        <MDPreview
                                            source={blog.content}
                                            className="!bg-transparent"
                                        />
                                    )}
                                    {!blog.content && (
                                        <div className="text-white/60 p-4 text-center">
                                            No content available
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </TerminalWindow>
            </motion.div>

            {isModalOpen && (
                <BlogModal
                    blog={blog}
                    onClose={() => {
                        setIsModalOpen(false);
                        loadBlog();
                    }}
                />
            )}
        </BaseLayout>
    );
};

export default BlogPostDetail;